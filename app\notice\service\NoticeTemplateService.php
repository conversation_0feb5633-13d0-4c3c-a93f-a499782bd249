<?php
declare(strict_types=1);

namespace app\notice\service;

use app\common\core\base\BaseService;
use app\common\core\crud\traits\CrudServiceTrait;
use app\common\exception\BusinessException;
use app\common\utils\CacheUtil;
use app\notice\model\NoticeTemplateModel;
use app\notice\service\interfaces\TemplateServiceInterface;
use think\facade\Log;

/**
 * todo 不使用model操作，使用封装的CrudServiceTrait中的对应方法操作
 * 消息模板表服务类
 */
class NoticeTemplateService extends BaseService implements TemplateServiceInterface
{
	
	use CrudServiceTrait;
	
	/**
	 * 单例实例
	 *
	 * @var NoticeTemplateService|null
	 */
	protected static ?NoticeTemplateService $instance = null;
	
	/**
	 * 模板缓存键前缀
	 */
	protected const TEMPLATE_CACHE_PREFIX = 'notice:template:';
	
	/**
	 * 租户模板配置服务实例
	 */
	protected ?TemplateConfigService $templateConfigService = null;
	
	/**
	 * 构造函数
	 */
	protected function __construct()
	{
		$this->model                 = new NoticeTemplateModel();
		$this->templateConfigService = TemplateConfigService::getInstance();
		parent::__construct();
		$this->crudService->setEnableDataPermission(false);
	}
	
	/**
	 * 获取单例
	 *
	 * @return static
	 */
	public static function getInstance(): self
	{
		if (is_null(static::$instance)) {
			static::$instance = new static();
		}
		return static::$instance;
	}
	
	/**
	 * todo 重复调用太多，是否可优化？根据模板编码获取模板
	 *
	 * @param string $code 模板编码
	 * @return array|null 模板数据
	 */
	public function getTemplateByCode(string $code): ?array
	{
		// 先从缓存获取
		$cacheKey = self::TEMPLATE_CACHE_PREFIX . $code;
		$template = CacheUtil::get($cacheKey);
		
		if ($template === null) {
			// 缓存未命中，从数据库获取
			$templateData = $this->model->where('code', $code)
			                            ->where('status', 1)
			                            ->find();
			
			if (!$templateData) {
				return null;
			}
			
			$template = $templateData->toArray();
			
			// 写入缓存
			CacheUtil::set($cacheKey, $template, 3600);
		}
		
		return $template;
	}
	
	/**
	 * 根据模板编码和租户ID获取模板及其租户配置
	 *
	 * @param string $code     模板编码
	 * @param int    $tenantId 租户ID
	 * @return array|null 模板数据及配置
	 */
	public function getTemplateWithConfig(string $code, int $tenantId): ?array
	{
		$template = $this->getTemplateByCode($code);
		
		if (!$template) {
			return null;
		}
		
		// 获取租户模板配置
		$config = $this->templateConfigService->getTemplateConfig($tenantId, $code);
		
		// 如果租户配置不存在，创建默认配置
		if (!$config) {
			$defaultConfig = [
				'tenant_id'        => $tenantId,
				'template_id'      => $template['id'],
				'template_code'    => $code,
				'is_enabled'       => 1,
				'site_enabled'     => 1,
				'email_enabled'    => 0,
				'sms_enabled'      => 0,
				'wework_enabled'   => 0,
				'dingtalk_enabled' => 0,
				'webhook_enabled'  => 0,
			];
			
			$configId = $this->templateConfigService->saveTemplateConfig($defaultConfig);
			
			if ($configId) {
				$config = $this->templateConfigService->getTemplateConfig($tenantId, $code);
			}
		}
		
		// 合并模板和配置
		return array_merge($template, ['tenant_config' => $config ?? []]);
	}
	
	/**
	 * 获取模板支持的通道
	 *
	 * @param string $code     模板编码
	 * @param int    $tenantId 租户ID
	 * @return array 支持的通道列表
	 */
	public function getTemplateChannels(string $code, int $tenantId): array
	{
		return $this->templateConfigService->getEnabledChannels($tenantId, $code);
	}
	
	/**
	 * 渲染模板内容
	 *
	 * @param string $template  模板内容
	 * @param array  $variables 模板变量
	 * @return string 渲染后的内容
	 */
	public function renderTemplate(string $template, array $variables): string
	{
		if (empty($template)) {
			return '';
		}
		
		// 使用正则表达式查找并替换模板变量
		return preg_replace_callback('/\${([^}]+)}/', function ($matches) use ($variables) {
			$key = $matches[1];
			return $variables[$key] ?? '';
		}, $template);
	}
	
	/**
	 * 渲染模板标题和内容
	 *
	 * @param string $templateCode 模板编码
	 * @param array  $variables    模板变量
	 * @return array 渲染后的标题和内容 ['title' => '标题', 'content' => '内容']
	 */
	public function renderTemplateContent(string $templateCode, array $variables): array
	{
		$template = $this->getTemplateByCode($templateCode);
		
		if (!$template) {
			Log::error("模板不存在: {$templateCode}");
			return [
				'title'   => '',
				'content' => ''
			];
		}
		
		$title   = $this->renderTemplate($template['title'], $variables);
		$content = $this->renderTemplate($template['content'], $variables);
		
		return [
			'title'   => $title,
			'content' => $content
		];
	}
	
	/**
	 * 创建模板
	 *
	 * @param array $data 模板数据
	 * @return int|bool 成功返回模板ID，失败返回false
	 */
	public function createTemplate(array $data): int|bool
	{
		try {
			$result = $this->add($data);
			
			if ($result && isset($data['code'])) {
				// 清除可能存在的缓存
				CacheUtil::delete(self::TEMPLATE_CACHE_PREFIX . $data['code']);
				
				// 创建默认租户配置 (针对主租户)
				if (isset($data['tenant_id'])) {
					$defaultConfig = [
						'tenant_id'        => $data['tenant_id'],
						'template_id'      => $result,
						'template_code'    => $data['code'],
						'is_enabled'       => 1,
						'site_enabled'     => 1,
						'email_enabled'    => 0,
						'sms_enabled'      => 0,
						'wework_enabled'   => 0,
						'dingtalk_enabled' => 0,
						'webhook_enabled'  => 0,
					];
					
					$this->templateConfigService->saveTemplateConfig($defaultConfig);
				}
			}
			
			return $result;
		}
		catch (\Exception $e) {
			Log::error('创建模板失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 更新模板
	 *
	 * @param int   $id   模板ID
	 * @param array $data 模板数据
	 * @return bool 是否成功
	 */
	public function updateTemplate(int $id, array $data): bool
	{
		try {
			// 先获取旧数据，用于清除缓存
			$oldTemplate = $this->model->find($id);
			
			$result = $this->update($id, $data);
			
			if ($result) {
				// 清除旧模板缓存
				$oldTemplate && CacheUtil::delete(self::TEMPLATE_CACHE_PREFIX . $oldTemplate->code);
				
				// 清除新模板缓存(如果修改了code)
				isset($data['code']) && $oldTemplate && $data['code'] !== $oldTemplate->code && CacheUtil::delete(self::TEMPLATE_CACHE_PREFIX . $data['code']);
				
				// 更新租户配置中的模板编码
				isset($data['code']) && $oldTemplate && $data['code'] !== $oldTemplate->code && $this->updateTenantConfigTemplateCode($id, $oldTemplate->code, $data['code']);
			}
			
			return $result;
		}
		catch (\Exception $e) {
			Log::error('更新模板失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 更新租户配置中的模板编码
	 *
	 * @param int    $templateId 模板ID
	 * @param string $oldCode    旧编码
	 * @param string $newCode    新编码
	 * @return void
	 */
	protected function updateTenantConfigTemplateCode(int $templateId, string $oldCode, string $newCode): void
	{
		try {
			// 这里假设有一个更新模板编码的方法
			$this->templateConfigService->updateTemplateCode($templateId, $oldCode, $newCode);
		}
		catch (\Exception $e) {
			Log::error('更新租户配置模板编码失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 删除模板
	 *
	 * @param int $id 模板ID
	 * @return bool 是否成功
	 */
	public function deleteTemplate(int $id): bool
	{
		try {
			// 先获取模板，用于清除缓存
			$template = $this->model->find($id);
			
			if (!$template) {
				throw new BusinessException('数据不存在');
			}
			
			$result = $template->delete();
			
			if ($result) {
				// 清除缓存
				CacheUtil::delete(self::TEMPLATE_CACHE_PREFIX . $template->code);
				
				// 删除关联的租户配置
				$this->deleteTemplateConfigs($id);
			}
			
			return $result;
		}
		catch (\Exception $e) {
			Log::error('删除模板失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 删除模板关联的租户配置
	 *
	 * @param int $templateId 模板ID
	 * @return void
	 */
	protected function deleteTemplateConfigs(int $templateId): void
	{
		try {
			// 假设实现了根据模板ID删除所有租户配置的方法
			$this->templateConfigService->deleteTemplateConfigsByTemplateId($templateId);
		}
		catch (\Exception $e) {
			Log::error('删除模板关联的租户配置失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 获取模板列表
	 *
	 * @param array $filters    过滤条件
	 * @param array $pagination 分页参数
	 * @return array 模板列表
	 */
	public function getTemplateList(array $filters = [], array $pagination = []): array
	{
		try {
			$page     = $pagination['page'] ?? 1;
			$pageSize = $pagination['page_size'] ?? 15;
			
			$query = $this->model->where('status', 1);
			
			// 应用过滤条件 - 使用链式调用而非多个if
			!empty($filters['type']) && $query->where('type', $filters['type']);
			!empty($filters['module_code']) && $query->where('module_code', $filters['module_code']);
			!empty($filters['sub_type']) && $query->where('sub_type', $filters['sub_type']);
			
			// 关键词搜索
			!empty($filters['keyword']) && $query->where(function ($q) use ($filters) {
				$keyword = "%{$filters['keyword']}%";
				$q->where('name', 'like', $keyword)
				  ->whereOr('code', 'like', $keyword);
			});
			
			$total = $query->count();
			$list  = $query->page($page, $pageSize)
			               ->select()
			               ->toArray();
			
			return [
				'list'      => $list,
				'total'     => $total,
				'page'      => $page,
				'page_size' => $pageSize
			];
		}
		catch (\Exception $e) {
			Log::error('获取模板列表失败: ' . $e->getMessage());
			return [
				'list'      => [],
				'total'     => 0,
				'page'      => $pagination['page'] ?? 1,
				'page_size' => $pagination['page_size'] ?? 15
			];
		}
	}
	
	/**
	 * 预览模板
	 *
	 * @param string $templateCode 模板编码
	 * @param array  $variables    模板变量
	 * @return array 预览结果 ['title' => '标题', 'content' => '内容']
	 */
	public function previewTemplate(string $templateCode, array $variables): array
	{
		return $this->renderTemplateContent($templateCode, $variables);
	}
	
	/**
	 * 从模板内容中提取变量
	 *
	 * @param string $content 模板内容
	 * @return array 变量列表
	 */
	public function extractVariablesFromContent(string $content): array
	{
		preg_match_all('/\${([^}]+)}/', $content, $matches);
		$variables = [];
		
		if (!empty($matches[1])) {
			foreach ($matches[1] as $varName) {
				// 去重
				$code = $this->generateVariableCode($varName);
				if (!isset($variables[$code])) {
					$variables[$code] = [
						'name'        => $varName,
						'code'        => $code,
						'field'       => '',
						'required'    => true,
						'description' => ''
					];
				}
			}
		}
		
		return array_values($variables);
	}
	
	/**
	 * 生成变量代码
	 *
	 * @param string $name 变量名称
	 * @return string 变量代码
	 */
	protected function generateVariableCode(string $name): string
	{
		// 简单映射表
		$map = [
			'标题'       => 'title',
			'内容'       => 'content',
			'任务名称'   => 'task_name',
			'流程标题'   => 'process_title',
			'提交人姓名' => 'submitter_name',
			'提交时间'   => 'submit_time',
			'审批结果'   => 'approval_result',
			'审批意见'   => 'approval_opinion',
			'审批人'     => 'approver_name',
			'审批时间'   => 'approve_time',
			'催办人'     => 'urger_name',
			'催办时间'   => 'urge_time',
			'催办原因'   => 'urge_reason',
			'客户名称'   => 'customer_name',
			'创建人'     => 'creator_name',
			'创建时间'   => 'create_time',
		];
		
		return $map[$name] ?? $this->convertToPinyin($name);
	}
	
	/**
	 * 转换中文为拼音
	 *
	 * @param string $text 中文文本
	 * @return string 拼音
	 */
	protected function convertToPinyin(string $text): string
	{
		// 简化实现，实际项目中应使用专门的拼音库
		$text = preg_replace('/[^\p{L}\p{N}]/u', '_', $text);
		return strtolower(preg_replace('/[^a-zA-Z0-9_]/', '', $text));
	}
	
	/**
	 * 合并变量配置
	 *
	 * @param array $extractedVars 提取的变量
	 * @param array $existingVars  已存在的变量
	 * @return array 合并后的变量
	 */
	protected function mergeVariablesConfig(array $extractedVars, array $existingVars): array
	{
		$result      = [];
		$existingMap = [];
		
		// 索引已有变量
		foreach ($existingVars as $var) {
			if (!empty($var['name'])) {
				$existingMap[$var['name']] = $var;
			}
		}
		
		// 合并变量
		foreach ($extractedVars as $var) {
			if (isset($existingMap[$var['name']])) {
				// 保留已有配置
				$result[] = $existingMap[$var['name']];
			}
			else {
				// 添加新变量
				$result[] = $var;
			}
			
			// 从已有变量中移除，剩下的是不再使用的变量
			unset($existingMap[$var['name']]);
		}
		
		// 保留那些没有出现在新提取变量中，但仍然存在于已有配置中的变量
		foreach ($existingVars as $var) {
			if (!empty($var['name']) && isset($existingMap[$var['name']])) {
				$result[] = $var;
			}
		}
		
		return $result;
	}
	
	/**
	 * 保存模板前处理数据
	 *
	 * @param array $data     模板数据
	 * @param bool  $isUpdate 是否是更新操作
	 * @return array 处理后的数据
	 */
	public function beforeSave(array $data, bool $isUpdate = false): array
	{
		// 如果没有提供变量配置，则自动提取
		if (!isset($data['variables_config'])) {
			// 提取标题和内容中的变量
			$title         = $data['title'] ?? '';
			$content       = $data['content'] ?? '';
			$extractedVars = $this->extractVariablesFromContent($title . "\n" . $content);
			
			// 获取旧的变量配置（如果是更新操作）
			$oldConfig = [];
			if ($isUpdate && !empty($data['id'])) {
				$template = $this->getById($data['id']);
				if ($template && !empty($template['variables_config'])) {
					$oldConfig = json_decode($template['variables_config'], true);
				}
			}
			
			// 合并新旧变量配置
			$mergedVars = $this->mergeVariablesConfig($extractedVars, $oldConfig['variables'] ?? []);
			
			// 更新数据
			$data['variables_config'] = json_encode(['variables' => $mergedVars], JSON_UNESCAPED_UNICODE);
		}
		elseif (is_array($data['variables_config'])) {
			// 如果提供的是数组，则转换为JSON
			$data['variables_config'] = json_encode($data['variables_config'], JSON_UNESCAPED_UNICODE);
		}
		
		return $data;
	}
	
	/**
	 * 重写添加方法，处理变量配置
	 *
	 * @param array $data 模板数据
	 * @return int|bool 模板ID或false
	 */
	public function add(array $data)
	{
		$data = $this->beforeSave($data, false);
		return $this->crudService->add($data);
	}
	
	/**
	 * 重写更新方法，处理变量配置
	 *
	 * @param int   $id   模板ID
	 * @param array $data 模板数据
	 * @return bool 是否成功
	 */
	public function update($id, array $data)
	{
		$data = $this->beforeSave($data, true);
		return $this->crudService->edit($data, [
			'id' => $id
		]);
	}
} 