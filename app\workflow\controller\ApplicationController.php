<?php
declare(strict_types=1);

namespace app\workflow\controller;

use app\common\core\base\BaseController;
use app\common\exception\BusinessException;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\service\ModuleService;
use app\workflow\service\WorkflowInstanceService;
use app\workflow\service\WorkflowDefinitionService;
use app\workflow\service\WorkflowEngineService;
use think\facade\Db;
use think\facade\Log;
use think\response\Json;

/**
 * 工作流程申请控制器
 * @route("myapp")
 */
class ApplicationController extends BaseController
{
	/**
	 * @var WorkflowInstanceService
	 */
	protected WorkflowInstanceService $service;
	
	/**
	 * 构造函数
	 */
	public function initialize(): void
	{
		parent::initialize();
		$this->service = WorkflowInstanceService::getInstance();
	}
	
	/**
	 * 我的申请列表
	 */
	public function index(): Json
	{
		// 获取查询参数并添加当前用户条件
		$params = $this->request->get();

		// 支持的业务类型（模式二通用页面集成）
		$supportedBusinessCodes = [
			'hr_leave',                        // 请假申请（原有）
			'ims_outbound_approval',           // 出库申请
			'ims_inbound_approval',            // 入库申请
			'ims_shipment_approval',           // 出货申请
			'ims_purchase_approval',           // 采购申请
			'finance_payment_approval',        // 付款申请
			'finance_expense_reimbursement',   // 报销申请
			'hr_business_trip',                // 出差申请
			'hr_outing',                       // 外出申请
			'office_sample_mail',              // 样品邮寄申请
			'office_procurement'               // 办公采购申请
		];

		// 如果没有指定business_code，则显示所有支持的类型
		if (!isset($params['business_code']) || empty($params['business_code'])) {
			$params['business_code'] = $supportedBusinessCodes;
		} else {
			// 验证请求的business_code是否在支持列表中
			$requestedCodes = is_array($params['business_code']) ? $params['business_code'] : [$params['business_code']];
			$params['business_code'] = array_intersect($requestedCodes, $supportedBusinessCodes);

			if (empty($params['business_code'])) {
				return $this->error('不支持的业务类型');
			}
		}

		// 调用服务层搜索方法
		$result = $this->service->search($params, [], [
			'types',
			'submitter'
		]);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取申请类型列表
	 */
	public function ModuleTypes(): Json
	{
		// 获取申请类型列表
		return $this->success('', ModuleService::getModuleOptions(true));
	}

	/**
	 * 获取支持的业务类型列表
	 */
	public function getSupportedBusinessTypes(): Json
	{
		$businessTypes = [
			[
				'code' => 'hr_leave',
				'name' => '请假申请',
				'module' => 'hr',
				'description' => '员工请假申请'
			],
			[
				'code' => 'ims_outbound_approval',
				'name' => '出库申请',
				'module' => 'ims',
				'description' => '库存商品出库申请'
			],
			[
				'code' => 'ims_inbound_approval',
				'name' => '入库申请',
				'module' => 'ims',
				'description' => '商品入库申请'
			],
			[
				'code' => 'ims_shipment_approval',
				'name' => '出货申请',
				'module' => 'ims',
				'description' => '从供应商虚拟仓发货申请'
			],
			[
				'code' => 'ims_purchase_approval',
				'name' => '采购申请',
				'module' => 'ims',
				'description' => '商品采购申请'
			],
			[
				'code' => 'finance_payment_approval',
				'name' => '付款申请',
				'module' => 'finance',
				'description' => '财务付款申请'
			],
			[
				'code' => 'finance_expense_reimbursement',
				'name' => '报销申请',
				'module' => 'finance',
				'description' => '费用报销申请'
			],
			[
				'code' => 'hr_business_trip',
				'name' => '出差申请',
				'module' => 'hr',
				'description' => '员工出差申请'
			],
			[
				'code' => 'hr_outing',
				'name' => '外出申请',
				'module' => 'hr',
				'description' => '员工外出申请'
			],
			[
				'code' => 'office_sample_mail',
				'name' => '样品邮寄申请',
				'module' => 'office',
				'description' => '样品邮寄申请'
			],
			[
				'code' => 'office_procurement',
				'name' => '办公采购申请',
				'module' => 'office',
				'description' => '办公用品、设备等采购申请'
			]
		];

		return $this->success('获取成功', $businessTypes);
	}
	
	/**
	 * 获取申请详情
	 *
	 * @param int $id 工作流实例ID
	 */
	public function detail(int $id): Json
	{
		// 调用服务获取详情
		$result = $this->service->getDetailWithExtra($id, [
			'submitter',
			'types',
			'dept'
		]);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取流程图数据
	 *
	 * @param int $id 工作流实例ID
	 */
	public function processGraph(int $id): Json
	{
		// 调用服务获取流程图数据
		$result = $this->service->getProcessGraph($id);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取审批历史记录
	 *
	 * @param int $id 工作流实例ID
	 */
	public function history(int $id): Json
	{
		// 调用服务获取历史记录
		$result = $this->service->getApprovalHistory($id);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 新增或编辑
	 * 表单中的保存按钮
	 */
	public function save(): Json
	{
		// 获取请求参数
		$params = $this->request->post();
		
		// 添加当前用户信息
		$this->addUserInfo($params);
		
		// 开启事务
		Db::startTrans();
		// 调用服务保存草稿
		try {
			$result = $this->service->saveApplicationDraft($params);
			Db::commit();
			return $this->success('', $result);
		}
		catch (BusinessException $e) {
			Db::rollback();
			return $this->error($e->getMessage());
		}
		catch (\Exception $e) {
			Db::rollback();
			return $this->error('保存失败');
		}
	}
	
	/**
	 * 新增/编辑表单中的提交按钮
	 */
	public function submit(): Json
	{
		Log::info('🚀 ApplicationController::submit 开始处理工作流申请提交');
		
		// 获取请求参数
		$params = $this->request->post();
		Log::info('📝 接收到的请求参数: ' . json_encode($params));
		
		// 添加当前用户信息
		$this->addUserInfo($params);
		Log::info('👤 添加用户信息后的参数: ' . json_encode([
			'submitter_id' => $params['submitter_id'] ?? 'N/A',
			'submitter_name' => $params['submitter_name'] ?? 'N/A',
			'submitter_dept_id' => $params['submitter_dept_id'] ?? 'N/A',
			'business_code' => $params['business_code'] ?? 'N/A'
		]));
		
		// 开启事务
		Log::info('🔄 开启数据库事务');
		Db::startTrans();
		try {
			// 调用服务提交申请
			Log::info('📤 调用 WorkflowInstanceService::submitApplication');
			$result = $this->service->submitApplication($params);
			Log::info('✅ 申请提交完成，结果: ' . json_encode($result));
			
			Db::commit();
			Log::info('✅ 数据库事务提交成功');
			
			return $this->success('', $result);
		}
		catch (BusinessException $e) {
			Db::rollback();
			Log::error('❌ 业务异常，事务已回滚: ' . $e->getMessage());
			return $this->error($e->getMessage());
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('❌ 系统异常，事务已回滚: ' . $e->getMessage());
			Log::error('异常堆栈: ' . $e->getTraceAsString());
			return $this->error('保存失败');
		}
	}
	
	/**
	 * 确认提交申请
	 * 表格列提交按钮
	 *
	 * @param int $id
	 * @return Json
	 */
	public function confirm(int $id): Json
	{
		// 开启事务
		Db::startTrans();
		
		try {
			// 获取工作流实例详情
			$instance = $this->service->detail($id);
			
			$adminId = $this->request->adminId;
			
			// 检查当前用户权限
			if ($instance['submitter_id'] != $adminId) {
				throw new BusinessException('无权操作此申请');
			}
			
			// 检查状态是否为草稿状态
			if ($instance['status'] != WorkflowStatusConstant::SAVED) {
				throw new BusinessException('只有草稿状态的申请可以确认提交');
			}
			
			// 更新实例状态
			$res = $this->service->edit([
				'status'     => WorkflowStatusConstant::APPROVING,
				'start_time' => date('Y-m-d H:i:s'),
			], [
				[
					'id',
					'=',
					$id,
				]
			]);
			
			if (!$res) {
				throw new BusinessException('更新申请状态失败');
			}
			
			// 重新获取最新实例信息
			$instance = $this->service->detail($id, ['submitter']);
			
			// 获取流程定义
			$definitionService = WorkflowDefinitionService::getInstance();
			$definition        = $definitionService->getModel()
			                                       ->where(['id' => $instance['definition_id']])
			                                       ->findOrEmpty();
			
			if ($definition->isEmpty()) {
				throw new BusinessException('流程定义不存在');
			}
			
			if (empty($definition->getAttr('status'))) {
				throw new BusinessException('流程已禁用');
			}
			
			// 解析流程配置
			$flowConfig = $definition->getAttr('flow_config');
			if (empty($flowConfig)) {
				throw new BusinessException('流程配置为空');
			}
			
			// 调用工作流引擎启动流程
			$workflowEngineService = new WorkflowEngineService();
			$engineResult          = $workflowEngineService->startWorkflow($instance->toArray());
			
			if (!$engineResult) {
				Log::error('工作流引擎启动失败, 实例ID: ' . $instance['id']);
				throw new BusinessException('工作流引擎启动失败');
			}
			
			Db::commit();
			return $this->success('提交成功');
		}
		catch (BusinessException $e) {
			Db::rollback();
			return $this->error($e->getMessage());
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('确认提交申请失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
			return $this->error('提交失败');
		}
	}
	
	/**
	 * 撤回申请，只有审批中的申请可撤回
	 *
	 * @param int $id 工作流实例ID
	 */
	public function recall(int $id): Json
	{
		// 检查是否为当前用户的申请
		$instance = $this->service->getOne([
			'id'           => $id,
			'submitter_id' => $this->request->adminId
		]);
		
		if (!$instance) {
			return $this->error('未找到申请或无权限操作');
		}
		
		// 检查状态是否允许撤回(只有审批中的申请可撤回)
		if ($instance['status'] != WorkflowStatusConstant::APPROVING) {
			return $this->error('当前状态不允许撤回');
		}
		
		// 调用服务撤回申请
		$result = $this->service->recallApplication($id);
		
		return $result
			? $this->success('撤回成功')
			: $this->error('撤回失败');
	}
	
	/**
	 * 删除申请
	 *
	 * @param int $id 工作流实例ID
	 */
	public function delete(int $id): Json
	{
		// 调用服务删除申请
		$result = $this->service->deleteApplication($id);
		
		return $result
			? $this->success('删除成功')
			: $this->error('删除失败');
	}
	
	/**
	 * 作废申请
	 *
	 * @param int $id 申请ID
	 * @return Json 响应结果
	 */
	public function void(int $id): Json
	{
		try {
			// 1. 获取作废原因
			$reason = $this->request->post('reason', '');
			
			// 2. 获取申请详情
			$application = $this->service->getOne([
				'id' => $id,
			]);
			if ($application->isEmpty()) {
				return $this->error('申请不存在');
			}
			
			// 3. 检查是否可以作废（只有已通过的申请可以作废）
			if ($application['status'] != WorkflowStatusConstant::APPROVED) {
				return $this->error('只有已通过的申请才能作废');
			}
			
			// 4. 调用通用的作废方法
			$result = $this->service->voidApplication($application['id'], $reason);
			
			return $result
				? $this->success('作废成功')
				: $this->error('作废失败');
			
		}
		catch (\Exception $e) {
			return $this->error('作废失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 添加用户信息到参数中
	 *
	 * @param array &$params 请求参数
	 */
	protected function addUserInfo(array &$params): void
	{
		$adminInfo = $this->request->adminInfo['data'] ?? [];
		// 添加当前用户ID
		$params['submitter_id'] = $params['admin_id'] = $this->request->adminId;
		
		// 添加当前用户名称
		$params['submitter_name'] = $adminInfo['real_name'] ?? '';
		
		// 添加当前用户部门信息
		$params['submitter_dept_id'] = $adminInfo['dept_id'];
	}
} 