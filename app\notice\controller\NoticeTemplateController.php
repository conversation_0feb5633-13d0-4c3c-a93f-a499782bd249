<?php
declare(strict_types=1);

namespace app\notice\controller;

use app\common\core\base\BaseController;
use app\common\exception\BusinessException;
use app\notice\model\NoticeTemplateModel;
use app\notice\service\NoticeTemplateService;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\facade\Validate;
use think\Request;
use think\response\Json;

/**
 * 通知模板控制器
 */
class NoticeTemplateController extends BaseController
{
	/**
	 * 请求对象
	 *
	 * @var Request
	 */
	protected $request;
	
	/**
	 * 获取模板列表
	 */
	public function list()
	{
		$params = $this->request->get();
		
		$page  = isset($params['page'])
			? intval($params['page'])
			: 1;
		$limit = isset($params['limit'])
			? intval($params['limit'])
			: 10;
		
		$where = [];
		
		// 模板名称
		if (!empty($params['name'])) {
			$where[] = [
				'name',
				'like',
				'%' . $params['name'] . '%'
			];
		}
		
		// 模板编码
		if (!empty($params['code'])) {
			$where[] = [
				'code',
				'like',
				'%' . $params['code'] . '%'
			];
		}
		
		// 模板类型
		if (isset($params['type']) && $params['type'] !== '') {
			$where[] = [
				'type',
				'=',
				$params['type']
			];
		}
		
		// 所属模块
		if (!empty($params['module_code'])) {
			$where[] = [
				'module_code',
				'=',
				$params['module_code']
			];
		}
		
		// 状态
		if (isset($params['status']) && $params['status'] !== '') {
			$where[] = [
				'status',
				'=',
				intval($params['status'])
			];
		}
		
		$total = NoticeTemplateModel::where($where)
		                            ->count();
		$list  = NoticeTemplateModel::where($where)
		                            ->order('id', 'desc')
		                            ->page($page, $limit)
		                            ->select();
		
		return $this->success('', [
			'list'  => $list,
			'total' => $total
		]);
	}
	
	/**
	 * 获取模板详情
	 *
	 * @param int $id 模板ID
	 */
	public function detail($id)
	{
		$template = NoticeTemplateModel::find($id);
		
		if (!$template) {
			return $this->error('模板不存在');
		}
		
		return $this->success('', $template);
	}
	
	/**
	 * 创建模板
	 */
	public function save(): Json
	{
		$data = $this->request->post();
		
		// 验证数据
		$validate = Validate::rule([
			'code'        => 'require|regex:/^[a-z0-9_]+$/|unique:notice_template',
			'name'        => 'require',
			'title'       => 'require',
			'content'     => 'require',
			'type'        => 'require|in:system,personal',
			'module_code' => 'require',
		])
		                    ->message([
			                    'code.require'        => '模板编码不能为空',
			                    'code.regex'          => '模板编码只能包含小写字母、数字和下划线',
			                    'code.unique'         => '模板编码已存在',
			                    'name.require'        => '模板名称不能为空',
			                    'title.require'       => '模板标题不能为空',
			                    'content.require'     => '模板内容不能为空',
			                    'type.require'        => '模板类型不能为空',
			                    'type.in'             => '模板类型不正确',
			                    'module_code.require' => '所属模块不能为空',
		                    ]);
		
		if (!$validate->check($data)) {
			return $this->error($validate->getError());
		}
		
		Db::startTrans();
		try {
			$template                = new NoticeTemplateModel();
			$template->code          = $data['code'];
			$template->name          = $data['name'];
			$template->title         = $data['title'];
			$template->content       = $data['content'];
			$template->type          = $data['type'];
			$template->module_code   = $data['module_code'];
			$template->send_channels = $data['send_channels'] ?? '';
			$template->status        = isset($data['status'])
				? intval($data['status'])
				: 1;
			$template->remark        = $data['remark'] ?? '';
			$template->created_by    = $this->getUserId();
			$template->updated_by    = $this->getUserId();
			$template->save();
			
			Db::commit();
			return $this->success('创建成功', $template);
		}
		catch (\Exception $e) {
			Db::rollback();
			return $this->error('创建失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 更新模板
	 *
	 * @param int $id 模板ID
	 */
	public function update($id)
	{
		$template = NoticeTemplateModel::find($id);
		
		if (!$template) {
			return $this->error('模板不存在');
		}
		
		$data = $this->request->put();
		
		// 验证数据
		$validate = Validate::rule([
			'name'        => 'require',
			'title'       => 'require',
			'content'     => 'require',
			'module_code' => 'require',
		])
		                    ->message([
			                    'name.require'        => '模板名称不能为空',
			                    'title.require'       => '模板标题不能为空',
			                    'content.require'     => '模板内容不能为空',
			                    'module_code.require' => '所属模块不能为空',
		                    ]);
		
		if (!$validate->check($data)) {
			return $this->error($validate->getError());
		}
		
		Db::startTrans();
		try {
			$template->name          = $data['name'];
			$template->title         = $data['title'];
			$template->content       = $data['content'];
			$template->module_code   = $data['module_code'];
			$template->send_channels = $data['send_channels'] ?? '';
			$template->status        = isset($data['status'])
				? intval($data['status'])
				: $template->status;
			$template->remark        = $data['remark'] ?? '';
			$template->updated_by    = $this->getUserId();
			$template->save();
			
			// 更新缓存
			$service = NoticeTemplateService::getInstance();
			$service->updateTemplate($template->id, $template->toArray());
			
			Db::commit();
			return $this->success('更新成功', $template);
		}
		catch (\Exception $e) {
			Db::rollback();
			return $this->error('更新失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 删除模板
	 *
	 * @param int $id 模板ID
	 * @return Json
	 * @throws DataNotFoundException
	 * @throws DbException
	 * @throws ModelNotFoundException
	 */
	public function delete(int $id): Json
	{
		$template = NoticeTemplateModel::find($id);
		
		if (!$template) {
			return $this->error('模板不存在');
		}
		
		Db::startTrans();
		try {
			// 使用服务类的deleteTemplate方法删除模板并清除缓存
			$service = NoticeTemplateService::getInstance();
			$result  = $service->deleteTemplate($id);
			
			if (!$result) {
				throw new BusinessException('删除失败');
			}
			
			Db::commit();
			return $this->success('删除成功');
		}
		catch (\Exception $e) {
			Db::rollback();
			return $this->error('删除失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 设置模板状态
	 *
	 * @param int $id 模板ID
	 */
	public function status($id)
	{
		$template = NoticeTemplateModel::find($id);
		
		if (!$template) {
			return $this->error('模板不存在');
		}
		
		$data = $this->request->put();
		
		if (!isset($data['status']) || !in_array($data['status'], [
				0,
				1
			])) {
			return $this->error('状态参数错误');
		}
		
		// 使用NoticeTemplateService更新模板并清除缓存
		$service = NoticeTemplateService::getInstance();
		$result  = $service->updateTemplate($id, [
			'status'     => $data['status'],
			'updated_by' => $this->getUserId()
		]);
		
		if ($result) {
			return $this->success('', '状态更新成功');
		}
		else {
			return $this->error('状态更新失败');
		}
	}
	
	/**
	 * 获取模块选项
	 */
	public function moduleOptions()
	{
		// 这里可以从配置或数据库中获取模块选项
		$options = [
			[
				'label' => 'CRM',
				'value' => 'crm'
			],
			[
				'label' => '进销存',
				'value' => 'erp'
			],
			[
				'label' => '办公',
				'value' => 'oa'
			],
			[
				'label' => '工作流',
				'value' => 'workflow'
			],
			[
				'label' => '财务',
				'value' => 'finance'
			],
			[
				'label' => '系统',
				'value' => 'system'
			],
		];
		
		return $this->success('', $options);
	}
	
	/**
	 * 预览模板
	 */
	public function preview()
	{
		$data = $this->request->post();
		
		if (empty($data['title']) || empty($data['content'])) {
			return $this->error('模板标题和内容不能为空');
		}
		
		$variables = $data['variables'] ?? [];
		
		$service = NoticeTemplateService::getInstance();
		$title   = $service->renderTemplate($data['title'], $variables);
		$content = $service->renderTemplate($data['content'], $variables);
		
		return $this->success('', [
			'title'   => $title,
			'content' => $content
		]);
	}
	
	/**
	 * 获取当前用户ID
	 */
	protected function getUserId()
	{
		// 根据实际认证系统获取用户ID
		return $this->request->AdminId;
	}
	
	/**
	 * 提取模板变量
	 *
	 * @return \think\Response
	 */
	public function extractVariables()
	{
		$post    = $this->request->post();
		$title   = $post['title'] ?? '';
		$content = $post['content'] ?? '';
		
		$service   = NoticeTemplateService::getInstance();
		$variables = $service->extractVariablesFromContent($title . "\n" . $content);
		
		return $this->success('提取成功', ['variables' => $variables]);
	}
	
	/**
	 * 获取模板变量配置
	 *
	 * @param int $id 模板ID
	 * @return \think\Response
	 */
	public function getVariablesConfig($id)
	{
		$service = NoticeTemplateService::getInstance();
		
		// 先清除可能过期的缓存，确保获取到最新数据
		$template = NoticeTemplateModel::find($id);
		if ($template) {
			// 获取模板数据时不清除缓存，只是确保读取到最新数据
			$template = $service->getTemplateByCode($template->code);
		}
		
		if (!$template) {
			return $this->error('模板不存在');
		}
		
		$config = json_decode($template['variables_config'] ?? '{}', true);
		
		return $this->success('获取成功', $config);
	}
	
	/**
	 * 保存模板变量配置
	 *
	 * @param int $id 模板ID
	 * @return \think\Response
	 */
	public function saveVariablesConfig(int $id)
	{
		$post   = $this->request->post();
		$config = $post['config'] ?? [];
		
		$service = NoticeTemplateService::getInstance();
		$result  = $service->update($id, ['variables_config' => $config]);
		
		if ($result) {
			// 清除模板缓存
			$template = NoticeTemplateModel::find($id);
			if ($template) {
				// 使用updateTemplate方法更新缓存
				$service->updateTemplate($id, ['variables_config' => $config]);
			}
			return $this->success('保存成功');
		}
		
		return $this->error('保存失败');
	}
} 