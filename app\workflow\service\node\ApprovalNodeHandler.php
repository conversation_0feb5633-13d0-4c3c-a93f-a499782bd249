<?php
declare(strict_types=1);

namespace app\workflow\service\node;

use app\notice\service\NoticeDispatcherService;
use app\workflow\constants\WorkflowStatusConstant;
// WorkflowOperationConstant已合并到WorkflowStatusConstant
use app\workflow\service\WorkflowContext;
use app\workflow\service\WorkflowTaskService;
use app\workflow\service\WorkflowHistoryService;
use app\workflow\service\WorkflowInstanceService;
use app\workflow\service\WorkflowUrlService;
use think\facade\Log;

/**
 * 审批节点处理器
 */
class ApprovalNodeHandler extends AbstractNodeHandler
{
    /**
     * 处理审批节点
     *
     * @param array $instance 工作流实例
     * @param array $node 当前节点
     * @param WorkflowContext $context 工作流上下文
     * @return bool
     */
    protected function handleNode(array $instance, array $node, WorkflowContext $context): bool
    {
        try {
            Log::info('处理审批节点: ' . ($node['nodeName'] ?? '未知') . ', 节点ID: ' . ($node['nodeId'] ?? '未知'));
            
            // 验证节点类型是否为审批节点
            if (($node['type'] ?? '') != '1' && ($node['type'] ?? '') != 1) {
                Log::error('节点类型错误，期望审批节点(type=1)，实际类型: ' . ($node['type'] ?? '未知'));
                return false;
            }
            
            // 增加详细日志记录提交人信息
            $submitterId = intval($instance['submitter_id'] ?? 0);
            Log::info('提交人ID: ' . $submitterId);
            
            // 更新当前节点
            WorkflowInstanceService::getInstance()->edit(
                ['current_node' => $node['nodeId']], 
                ['id' => $instance['id']]
            );
            
            // 根据setType获取审批人列表
            $setType = intval($node['setType'] ?? 1);
            Log::info('审批人设置类型: ' . $setType);

            $approvers = $this->getApproversBySetType($instance, $node, $setType, $submitterId);
            Log::info('最终审批人列表: ' . json_encode($approvers));

            if (empty($approvers)) {
                Log::error('审批人列表为空: ' . json_encode($node));
                // TODO: 实现无处理人情况的处理逻辑，根据noHandlerAction字段决定如何处理
                // noHandlerAction: 1=自动通过，2=自动驳回，3=转交给管理员
                return false;
            }
            
            // 获取审批模式
            $approvalMode = intval($node['approvalMode'] ?? WorkflowStatusConstant::APPROVAL_MODE_ALL);
            
            Log::info('审批节点模式: ' . $this->getApprovalModeName($approvalMode));
            
            // 打印更多详细信息便于调试
            Log::info('节点配置详情: ' . json_encode($node));
            
            // 根据不同的审批模式处理
            switch ($approvalMode) {
                case WorkflowStatusConstant::APPROVAL_MODE_ANY:
                    // 任意一人通过即可
                    return $this->handleAnyoneApproveMode($instance, $node, $approvers, $submitterId, $context);
                    
                case WorkflowStatusConstant::APPROVAL_MODE_SEQUENCE:
                    // 按顺序依次审批
                    return $this->handleSequentialApproveMode($instance, $node, $approvers, $submitterId, $context);
                    
                case WorkflowStatusConstant::APPROVAL_MODE_ALL:
                default:
                    // 所有人通过（默认模式）
                    return $this->handleAllApproveMode($instance, $node, $approvers, $submitterId, $context);
            }
        } catch (\Exception $e) {
            Log::error('处理审批节点异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }
    
    /**
     * 获取审批模式名称
     *
     * @param int $mode 审批模式
     * @return string 模式名称
     */
    private function getApprovalModeName(int $mode): string
    {
        switch ($mode) {
            case WorkflowStatusConstant::APPROVAL_MODE_ANY:
                return '任意一人通过即可';
            case WorkflowStatusConstant::APPROVAL_MODE_SEQUENCE:
                return '按顺序依次审批';
            case WorkflowStatusConstant::APPROVAL_MODE_ALL:
                return '所有人通过';
            default:
                return '未知模式(' . $mode . ')';
        }
    }

    /**
     * 根据setType获取审批人列表
     *
     * @param array $instance 工作流实例
     * @param array $node 当前节点
     * @param int $setType 设置类型
     * @param int $submitterId 提交人ID
     * @return array
     */
    private function getApproversBySetType(array $instance, array $node, int $setType, int $submitterId): array
    {
        switch ($setType) {
            case 1: // ASSIGN - 指定成员
                Log::info('使用指定成员模式');
                return $node['nodeUserList'] ?? [];

            case 4: // LEADER - 部门主管
                Log::info('使用部门主管模式');
                return $this->getDirectorApprovers($instance, $node, $submitterId);

            case 2: // SELF_SELECT - 发起人自选
                Log::info('使用发起人自选模式');
                // 发起人自选模式下，审批人应该在提交时由前端传入
                // 这里暂时返回配置的默认审批人
                return $node['nodeUserList'] ?? [];

            case 8: // ROLE - 角色
                Log::info('使用角色模式');
                return $this->getRoleApprovers($node);

            case 16: // FORM_USER - 表单人员（动态）
                Log::info('使用表单人员模式');
                return $this->getFormUserApprovers($instance, $node);

            default:
                Log::warning('未知的setType: ' . $setType . '，使用默认指定成员模式');
                return $node['nodeUserList'] ?? [];
        }
    }

    /**
     * 获取部门主管审批人
     *
     * @param array $instance 工作流实例
     * @param array $node 当前节点
     * @param int $submitterId 提交人ID
     * @return array
     */
    private function getDirectorApprovers(array $instance, array $node, int $submitterId): array
    {
        $directorLevel = intval($node['directorLevel'] ?? 1);
        Log::info('查找部门主管，层级: ' . $directorLevel);

        try {
            // 获取提交人信息
            $submitter = \app\system\model\AdminModel::find($submitterId);
            if (!$submitter || !$submitter->dept_id) {
                Log::warning('提交人不存在或未设置部门，提交人ID: ' . $submitterId);
                return [];
            }

            // 查找指定层级的主管
            $currentDeptId = $submitter->dept_id;
            $currentLevel = 0;

            while ($currentLevel < $directorLevel && $currentDeptId > 0) {
                $dept = \app\system\model\DeptModel::find($currentDeptId);
                if (!$dept) {
                    break;
                }

                $currentLevel++;

                if ($currentLevel === $directorLevel) {
                    // 找到目标层级的部门
                    if ($dept->leader_id) {
                        $leader = \app\system\model\AdminModel::find($dept->leader_id);
                        if ($leader) {
                            Log::info('找到' . $directorLevel . '级主管: ' . $leader->real_name . ' (ID: ' . $leader->id . ')');
                            return [
                                [
                                    'id' => $leader->id,
                                    'name' => $leader->real_name,
                                    'type' => 'user'
                                ]
                            ];
                        }
                    }
                    break;
                }

                // 继续向上查找
                $currentDeptId = $dept->parent_id;
            }

            Log::warning('未找到' . $directorLevel . '级主管');
            return [];

        } catch (\Exception $e) {
            Log::error('查找部门主管失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取角色审批人
     *
     * @param array $node 当前节点
     * @return array
     */
    private function getRoleApprovers(array $node): array
    {
        // TODO: 实现角色审批人查找逻辑
        Log::info('角色审批人功能待实现');
        return [];
    }

    /**
     * 获取表单人员审批人
     *
     * @param array $instance 工作流实例
     * @param array $node 当前节点
     * @return array
     */
    private function getFormUserApprovers(array $instance, array $node): array
    {
        // TODO: 实现表单人员审批人查找逻辑
        Log::info('表单人员审批人功能待实现');
        return [];
    }

    /**
     * 处理任意一人通过模式
     *
     * @param array $instance 工作流实例
     * @param array $node 当前节点
     * @param array $approvers 审批人列表
     * @param int $submitterId 提交人ID
     * @param WorkflowContext $context 工作流上下文
     * @return bool
     */
    private function handleAnyoneApproveMode(array $instance, array $node, array $approvers, int $submitterId, WorkflowContext $context): bool
    {
        Log::info('处理任意一人通过模式');
        
        // 保存审批模式到实例中，供后续处理使用
        WorkflowInstanceService::getInstance()->edit(
            ['node_config' => json_encode(['approvalMode' => WorkflowStatusConstant::APPROVAL_MODE_ANY])],
            ['id' => $instance['id']]
        );
        
        // ✅ 优化：移除单例服务，在循环中直接实例化模型
        $success = true;
        $hasAutoApproved = false;

        // 检查是否开启自动通过功能
        $enableAutoApprove = isset($node['autoApprove']) ? (bool)$node['autoApprove'] : true;

        // 检查当前节点是否已经有审批人与提交人相同的情况被处理过
        $historyService = WorkflowHistoryService::getInstance();
        $existingAutoApproval = $historyService->getModel()
            ->where([
                'instance_id' => $instance['id'],
                'node_id' => $node['nodeId'],
                'operator_id' => $submitterId,
                'operation' => WorkflowStatusConstant::OPERATION_AGREE,
                'opinion' => ['like', '%系统自动通过%']
            ])
            ->find();
        
        foreach ($approvers as $user) {
            $userId = isset($user['id']) ? intval($user['id']) : 0;
            
            // 检查是否已存在该用户的审批记录
            $userApprovalHistory = $historyService->getModel()
                ->where([
                    'instance_id' => $instance['id'],
                    'node_id' => $node['nodeId'],
                    'operator_id' => $userId,
                    'operation' => WorkflowStatusConstant::OPERATION_AGREE
                ])
                ->find();
                
            if ($userApprovalHistory) {
                Log::info('该用户已有审批记录，跳过: 用户ID=' . $userId);
                continue;
            }
            
            // 如果审批人是提交人，且启用了自动通过
            if ($enableAutoApprove && $userId === $submitterId && !$existingAutoApproval) {
                Log::info('发现审批人与提交人完全一致，执行自动通过: 用户ID=' . $userId);
                $this->autoApproveForSameUser($instance, $node, $user);
                $hasAutoApproved = true;
                
                // 任意一人通过模式下，如果有自动通过，则直接继续流程
                if ($hasAutoApproved && !empty($node['childNode'])) {
                    Log::info('任意一人通过模式：自动通过后继续流程');
                    // 在测试环境中，我们直接返回true，而不是调用handleNextNode
                    /*if (defined('PHPUNIT_RUNNING') || isset($_ENV['TESTING']) || isset($_ENV['APP_ENV']) && $_ENV['APP_ENV'] === 'testing') {
                        return true;
                    }*/
                    return $this->handleNextNode($instance, $node['childNode'], $context);
                }
                
                continue;
            }
            
            // ✅ 优化：直接创建任务模型，避免单例状态污染
            $result = $this->createTaskDirectly($instance, $node, $user);
            if (!$result) {
                Log::error('创建审批任务失败: 用户ID=' . $userId);
                $success = false;
            } else {
                Log::info('成功创建审批任务: 用户ID=' . $userId);
                $this->sendApprovalNotification($instance, $node, $user);
            }
        }
        
        return $success;
    }
    
    /**
     * 处理所有人通过模式
     *
     * @param array $instance 工作流实例
     * @param array $node 当前节点
     * @param array $approvers 审批人列表
     * @param int $submitterId 提交人ID
     * @param WorkflowContext $context 工作流上下文
     * @return bool
     */
    private function handleAllApproveMode(array $instance, array $node, array $approvers, int $submitterId, WorkflowContext $context): bool
    {
        Log::info('处理所有人通过模式');
        
        // 保存审批模式到实例中，供后续处理使用
        WorkflowInstanceService::getInstance()->edit(
            ['node_config' => json_encode(['approvalMode' => WorkflowStatusConstant::APPROVAL_MODE_ALL])],
            ['id' => $instance['id']]
        );
        
        // ✅ 优化：移除单例服务，在循环中直接实例化模型
        $success = true;
        $allAutoApproved = true;
        $hasAutoApproved = false;
        $totalApprovers = count($approvers);
        $autoApprovedCount = 0;

        // 检查是否开启自动通过功能
        $enableAutoApprove = isset($node['autoApprove']) ? (bool)$node['autoApprove'] : true;
        
        // 防止自动通过导致审批跳过：如果只有一个审批人且与提交人相同，不进行自动通过
        if ($totalApprovers === 1 && intval($approvers[0]['id']) === $submitterId && $enableAutoApprove) {
            Log::info('节点只有一个审批人且与提交人相同，禁用自动通过功能防止跳过审批');
            $enableAutoApprove = false;
        }
        
        // 检查当前节点是否已经有审批人与提交人相同的情况被处理过
        $historyService = WorkflowHistoryService::getInstance();
        $existingAutoApproval = $historyService->getModel()
            ->where([
                'instance_id' => $instance['id'],
                'node_id' => $node['nodeId'],
                'operator_id' => $submitterId,
                'operation' => WorkflowStatusConstant::OPERATION_AGREE,
                'opinion' => ['like', '%系统自动通过%']
            ])
            ->find();
        
        foreach ($approvers as $user) {
            $userId = isset($user['id']) ? intval($user['id']) : 0;
            
            // 检查是否已存在该用户的审批记录
            $userApprovalHistory = $historyService->getModel()
                ->where([
                    'instance_id' => $instance['id'],
                    'node_id' => $node['nodeId'],
                    'operator_id' => $userId,
                    'operation' => WorkflowStatusConstant::OPERATION_AGREE
                ])
                ->find();
                
            if ($userApprovalHistory) {
                Log::info('该用户已有审批记录，跳过: 用户ID=' . $userId);
                continue;
            }
            
            // 如果审批人是提交人，且启用了自动通过
            if ($enableAutoApprove && $userId === $submitterId && !$existingAutoApproval) {
                Log::info('发现审批人与提交人完全一致，执行自动通过: 用户ID=' . $userId);
                $this->autoApproveForSameUser($instance, $node, $user);
                $hasAutoApproved = true;
                $autoApprovedCount++;
                continue;
            } else {
                // ✅ 优化：直接创建任务模型，避免单例状态污染
                $result = $this->createTaskDirectly($instance, $node, $user);
                if (!$result) {
                    Log::error('创建审批任务失败: 用户ID=' . $userId);
                    $success = false;
                } else {
                    Log::info('成功创建审批任务: 用户ID=' . $userId);
                    $this->sendApprovalNotification($instance, $node, $user);
                }
                $allAutoApproved = false;
            }
        }
        
        // 记录自动流转条件判断
        Log::info('自动流转条件判断: 全部自动通过=' . ($allAutoApproved ? '是' : '否') . 
                 ', 有自动通过记录=' . ($hasAutoApproved ? '是' : '否') . 
                 ', 审批人总数=' . $totalApprovers . 
                 ', 自动通过数=' . $autoApprovedCount);
                 
        // 如果所有审批都是自动通过的，并且已经有自动通过的记录，继续处理下一节点
        if ($allAutoApproved && $hasAutoApproved && !empty($node['childNode'])) {
            Log::info('所有审批人都自动通过，继续处理下一个节点');
            return $this->handleNextNode($instance, $node['childNode'], $context);
        }
        
        return $success;
    }
    
    /**
     * 处理按顺序依次审批模式
     *
     * @param array $instance 工作流实例
     * @param array $node 当前节点
     * @param array $approvers 审批人列表
     * @param int $submitterId 提交人ID
     * @param WorkflowContext $context 工作流上下文
     * @return bool
     */
    private function handleSequentialApproveMode(array $instance, array $node, array $approvers, int $submitterId, WorkflowContext $context): bool
    {
        Log::info('处理按顺序依次审批模式');
        
        if (empty($approvers)) {
            Log::error('审批人列表为空');
            return false;
        }
        
        // 保存审批模式和剩余审批人到实例中，供后续处理使用
        $remainingApprovers = array_slice($approvers, 1);
        WorkflowInstanceService::getInstance()->edit(
            [
                'node_config' => json_encode([
                    'approvalMode' => WorkflowStatusConstant::APPROVAL_MODE_SEQUENCE,
                    'remaining_approvers' => $remainingApprovers
                ])
            ],
            ['id' => $instance['id']]
        );
        
        // ✅ 优化：移除单例服务，直接实例化模型
        $firstApprover = $approvers[0];
        $userId = isset($firstApprover['id']) ? intval($firstApprover['id']) : 0;

        // 检查是否开启自动通过功能
        $enableAutoApprove = isset($node['autoApprove']) ? (bool)$node['autoApprove'] : true;
        
        // 检查当前节点是否已经有审批人与提交人相同的情况被处理过
        $historyService = WorkflowHistoryService::getInstance();
        $existingAutoApproval = $historyService->getModel()
            ->where([
                'instance_id' => $instance['id'],
                'node_id' => $node['nodeId'],
                'operator_id' => $submitterId,
                'operation' => WorkflowStatusConstant::OPERATION_AGREE,
                'opinion' => ['like', '%系统自动通过%']
            ])
            ->find();
        
        // 如果第一个审批人是提交人，且启用了自动通过
        if ($enableAutoApprove && $userId === $submitterId && !$existingAutoApproval) {
            Log::info('发现第一个审批人与提交人完全一致，执行自动通过: 用户ID=' . $userId);
            $this->autoApproveForSameUser($instance, $node, $firstApprover);
            
            // 如果还有剩余审批人，创建下一个审批任务
            if (!empty($remainingApprovers)) {
                Log::info('顺序审批模式：第一个审批人自动通过，继续处理下一个审批人');
                $nextApprover = $remainingApprovers[0];
                $remainingApprovers = array_slice($remainingApprovers, 1);
                
                // 更新剩余审批人列表
                WorkflowInstanceService::getInstance()->edit(
                    [
                        'node_config' => json_encode([
                            'approvalMode' => WorkflowStatusConstant::APPROVAL_MODE_SEQUENCE,
                            'remaining_approvers' => $remainingApprovers
                        ])
                    ],
                    ['id' => $instance['id']]
                );
                
                // ✅ 优化：直接创建任务模型，避免单例状态污染
                $result = $this->createTaskDirectly($instance, $node, $nextApprover);
                if (!$result) {
                    Log::error('创建审批任务失败: 用户ID=' . $nextApprover['id']);
                    return false;
                }
                
                Log::info('成功创建审批任务: 用户ID=' . $nextApprover['id']);
                $this->sendApprovalNotification($instance, $node, $nextApprover);
                return true;
            } else {
                // 没有剩余审批人，继续流程
                if (!empty($node['childNode'])) {
                    Log::info('顺序审批模式：所有审批人都已处理，继续流程');
                    return $this->handleNextNode($instance, $node['childNode'], $context);
                }
                return true;
            }
        } else {
            // ✅ 优化：直接创建任务模型，避免单例状态污染
            $result = $this->createTaskDirectly($instance, $node, $firstApprover);
            if (!$result) {
                Log::error('创建审批任务失败: 用户ID=' . $userId);
                return false;
            }
            
            Log::info('成功创建审批任务: 用户ID=' . $userId);
            $this->sendApprovalNotification($instance, $node, $firstApprover);
            return true;
        }
    }
    
    /**
     * 发送审批通知
     *
     * @param array $instance 工作流实例
     * @param array $node 当前节点
     * @param array $user 用户信息
     */
    private function sendApprovalNotification(array $instance, array $node, array $user): void
    {
        Log::info('=== 开始发送审批通知 ===');
        Log::info('实例ID: ' . ($instance['id'] ?? 'N/A'));
        Log::info('节点名称: ' . ($node['nodeName'] ?? 'N/A'));
        Log::info('用户信息: ' . json_encode($user));
        
        try {
            $userId = isset($user['id']) ? intval($user['id']) : 0;
            if ($userId <= 0) {
                Log::error('❌ 无效的用户ID，跳过通知发送');
                return;
            }
            Log::info('✅ 用户ID验证通过: ' . $userId);

            // 准备变量 - 需要同时支持中英文键名以兼容不同的模板格式
            $variables = [
                // 英文键名（用于代码逻辑）
                'task_name'      => $node['nodeName'] ?? '审批任务',
                'title'          => $instance['title'] ?? '未命名流程',
                'submitter_name' => $instance['submitter_name'] ?? '系统',
                'created_at'     => $instance['created_at'] ?? date('Y-m-d H:i:s'),
                'detail_url'     => '/workflow/task/detail?instance_id=' . $instance['id'],
                
                // 中文键名（用于模板渲染）
                '任务名称'        => $node['nodeName'] ?? '审批任务',
                '流程标题'        => $instance['title'] ?? '未命名流程',
                '提交人'          => $instance['submitter_name'] ?? '系统',
                '提交时间'        => $instance['created_at'] ?? date('Y-m-d H:i:s'),
                '紧急程度'        => '普通', // 数据库模板中需要但当前未实现的字段
            ];
            Log::info('📋 准备的消息变量: ' . json_encode($variables));

            // 生成URL - 修复$task未定义问题
            try {
                $urls = WorkflowUrlService::generateBatchUrls('task.approval', [
                    'task_id' => $instance['id']  // 使用实例ID代替未定义的$task['id']
                ]);
                Log::info('🔗 生成的URLs: ' . json_encode($urls));
            } catch (\Exception $e) {
                Log::error('❌ URL生成失败: ' . $e->getMessage());
                $urls = ['detail_url' => '', 'mobile_url' => ''];
            }

            // 准备发送选项
            $options = [
                'business_id' => (string)$instance['id'],
                'detail_url' => $urls['detail_url'] ?? '',
                'mobile_url' => $urls['mobile_url'] ?? ''
            ];
            Log::info('⚙️ 发送选项: ' . json_encode($options));

            // 发送审批任务通知
            Log::info('📤 开始调用NoticeDispatcherService发送消息...');
            Log::info('模块名: ' . WorkflowStatusConstant::MODULE_NAME);
            Log::info('消息类型: ' . WorkflowStatusConstant::MESSAGE_TASK_APPROVAL);
            Log::info('接收人: [' . $userId . ']');
            
            $result = NoticeDispatcherService::getInstance()->send(
                WorkflowStatusConstant::MODULE_NAME,
                WorkflowStatusConstant::MESSAGE_TASK_APPROVAL,
                $variables,
                [$userId],
                $options
            );
            
            if ($result) {
                Log::info('✅ 审批通知发送成功: 用户ID=' . $userId . ', 返回结果=' . json_encode($result));
            } else {
                Log::error('❌ 审批通知发送失败: 用户ID=' . $userId . ', 返回结果=false');
            }
            
        } catch (\Exception $e) {
            Log::error('❌ 发送审批通知异常: ' . $e->getMessage());
            Log::error('异常堆栈: ' . $e->getTraceAsString());
        }
        
        Log::info('=== 审批通知发送流程结束 ===');
    }
    
    /**
     * 为相同用户自动审批
     *
     * @param array $instance 工作流实例
     * @param array $node 节点数据
     * @param array $user 审批人/提交人
     * @return bool
     */
    protected function autoApproveForSameUser(array $instance, array $node, array $user): bool
    {
        try {
            Log::info('执行自动审批: 实例ID=' . $instance['id'] . ', 节点=' . $node['nodeName'] . ', 用户ID=' . $user['id']);
            
            // 生成唯一标识，防止重复记录
            $uniqueId = md5($instance['id'] . $node['nodeId'] . $user['id'] . microtime(true));
            
            // 记录自动通过的审批历史
            $historyData = [
                'instance_id'       => $instance['id'],
                'process_id'        => $instance['process_id'],
                'task_id'           => '',
                'node_id'           => $node['nodeId'],
                'node_name'         => $node['nodeName'],
                'node_type'         => 'approval',
                'prev_node_id'      => $instance['current_node'],
                'operator_id'       => $user['id'],
                'operation'         => WorkflowStatusConstant::OPERATION_AGREE,
                'opinion'           => '系统自动通过(申请人与审批人相同)_' . $uniqueId,
                'operation_time'    => date('Y-m-d H:i:s'),
                'tenant_id'         => $instance['tenant_id'] ?? 0
            ];
            
            $historyId = WorkflowHistoryService::getInstance()->safeAddHistory($historyData);
            
            if ($historyId) {
                Log::info('自动审批历史记录创建成功，ID: ' . $historyId);
            } else {
                Log::info('自动审批历史记录已存在，跳过创建');
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('自动审批处理失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 直接创建任务，避免单例状态污染
     *
     * @param array $instance 工作流实例
     * @param array $node     节点数据
     * @param array $user     审批人
     * @return bool
     */
    private function createTaskDirectly(array $instance, array $node, array $user): bool
    {
        try {
            $userId = isset($user['id']) ? intval($user['id']) : 0;
            if ($userId <= 0) {
                Log::error('无效的用户ID: ' . json_encode($user));
                return false;
            }

            // 检查是否已存在相同的任务
            $existingTask = \app\workflow\model\WorkflowTask::where([
                'instance_id' => $instance['id'],
                'node_id'     => $node['nodeId'],
                'approver_id' => $userId,
                'status'      => 0,
                'deleted_at'  => null
            ])->findOrEmpty();

            if (!$existingTask->isEmpty()) {
                Log::info('已存在相同的待处理任务，跳过创建: 实例ID=' . $instance['id'] . ', 节点ID=' . $node['nodeId'] . ', 用户ID=' . $userId);
                return true;
            }

            // 创建新任务
            $taskId = md5($instance['id'] . $node['nodeId'] . $userId . microtime(true));
            $taskData = [
                'task_id'       => $taskId,
                'instance_id'   => $instance['id'],
                'process_id'    => $instance['process_id'],
                'node_id'       => $node['nodeId'],
                'node_name'     => $node['nodeName'] ?? '审批节点',
                'node_type'     => isset($node['type']) && $node['type'] == WorkflowStatusConstant::NODE_TYPE_COPYER
                    ? WorkflowStatusConstant::TASK_TYPE_CC
                    : WorkflowStatusConstant::TASK_TYPE_APPROVAL,
                'task_type'     => isset($node['type']) && $node['type'] == WorkflowStatusConstant::NODE_TYPE_COPYER
                    ? WorkflowStatusConstant::TASK_TYPE_CC
                    : WorkflowStatusConstant::TASK_TYPE_APPROVAL,
                'approver_id'   => $userId,
                'approver_name' => $user['real_name'] ?? $user['username'] ?? '',
                'status'        => 0,
                'tenant_id'     => $instance['tenant_id'] ?? 0
            ];

            // 直接实例化模型创建任务
            $taskModel = new \app\workflow\model\WorkflowTask();
            $result = $taskModel->saveByCreate($taskData);

            if ($result) {
                Log::info('成功创建审批任务: 实例ID=' . $instance['id'] . ', 节点ID=' . $node['nodeId'] . ', 用户ID=' . $userId);
                return true;
            } else {
                Log::error('创建审批任务失败: ' . json_encode($taskData));
                return false;
            }

        } catch (\Exception $e) {
            Log::error('创建审批任务异常: ' . $e->getMessage());
            return false;
        }
    }
}