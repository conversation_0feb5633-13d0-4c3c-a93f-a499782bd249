<?php
declare(strict_types=1);

namespace app\notice\service;

use app\common\core\base\BaseService;
use app\notice\service\interfaces\NoticeDispatcherInterface;
use think\facade\Log;

/**
 * 消息调度器服务
 * 提供统一的发送接口，根据模板变量配置自动提取变量
 */
class NoticeDispatcherService implements NoticeDispatcherInterface
{
    /**
     * 单例实例
     *
     * @var NoticeDispatcherService|null
     */
    protected static ?NoticeDispatcherService $instance = null;
    
    /**
     * 模板服务实例
     */
    protected ?NoticeTemplateService $templateService = null;
    
    /**
     * 消息服务实例
     */
    protected ?NoticeMessageService $messageService = null;
    
    /**
     * 构造函数
     */
    protected function __construct()
    {
        $this->templateService = NoticeTemplateService::getInstance();
        $this->messageService = NoticeMessageService::getInstance();
    }
    
    /**
     * 获取单例
     *
     * @return static
     */
    public static function getInstance(): self
    {
        if (is_null(static::$instance)) {
            static::$instance = new static();
        }
        return static::$instance;
    }
    
    /**
     * 发送模块消息
     * 
     * @param string $module 模块名称
     * @param string $action 动作名称
     * @param array $data 业务数据
     * @param array $recipients 接收人ID数组
     * @param array $options 选项
     * @return int|bool 成功返回消息ID，失败返回false
     */
    public function send(string $module, string $action, array $data, array $recipients, array $options = [])
    {
        // 构建模板编码
        $templateCode = "{$module}_{$action}";
        Log::info('📝 构建模板编码: ' . $templateCode);

        // 获取模板
        Log::info('🔍 开始查找消息模板...');
        $template = $this->templateService->getTemplateByCode($templateCode);
        if (!$template) {
            Log::error("❌ 消息发送失败: 模板不存在 code={$templateCode}");
            Log::error("请检查数据库中是否存在该模板记录");
            return false;
        }
        Log::info('✅ 找到消息模板: ' . json_encode($template));

        // 提取变量
        try {
            Log::info('🔧 开始提取变量...');
			// todo 此处需重点关注
            $variables = $this->extractVariables($data, $template);
            Log::info('📋 提取的变量: ' . json_encode($variables));

            // 验证必填变量
            Log::info('✔️ 开始验证必填变量...');
            $missingVars = $this->validateRequiredVariables($data, $template);
            Log::info('缺失的必填变量: ' . json_encode($missingVars));

            if (!empty($missingVars)) {
                Log::error("❌ 消息发送失败: 缺少必填变量 " . implode(', ', $missingVars) . " in template {$templateCode}");
                Log::error("模板变量配置: " . ($template['variables_config'] ?? 'N/A'));
                return false;
            }
            Log::info('✅ 必填变量验证通过');

            // 确保CLI环境下有正确的默认值
            /*$defaultOptions = [
                'creator_id' => 0,  // CLI环境下默认为系统创建
                'tenant_id'  => 0,  // CLI环境下默认租户
            ];
            $options = array_merge($defaultOptions, $options);
            Log::info('⚙️ 合并后的选项: ' . json_encode($options));*/

            // 发送消息
            Log::info('📤 开始调用MessageService发送消息...');
            $result = $this->messageService->send($templateCode, $variables, $recipients, $options);
            
            if ($result) {
                Log::info('✅ NoticeDispatcherService 发送成功，返回结果: ' . json_encode($result));
            } else {
                Log::error('❌ NoticeDispatcherService 发送失败，MessageService返回false');
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error("❌ 消息发送异常: " . $e->getMessage());
            Log::error("异常堆栈: " . $e->getTraceAsString());
            return false;
        }
    }
    
    /**
     * 根据变量配置提取数据
     *
     * @param array $data 业务数据
     * @param array $template 模板数据
     * @return array 变量数据
     */
    protected function extractVariables(array $data, array $template): array
    {
        $result = [];
        $config = json_decode($template['variables_config'] ?? '{}', true);

        if (isset($config['variables']) && is_array($config['variables'])) {
            foreach ($config['variables'] as $var) {
                $value = $this->getNestedValue($data, $var['field']);
                if ($value !== null) {
                    // 使用 name（中文名）作为键，这样模板中的 ${中文变量名} 就能匹配到值
                    $result[$var['name']] = $value;
                }
            }
        }

        return $result;
    }
    
    /**
     * 获取嵌套属性值
     *
     * @param array|object $data 数据对象
     * @param string $path 属性路径
     * @return mixed 属性值
     */
    protected function getNestedValue($data, string $path)
    {
        if (empty($path)) {
            return null;
        }
        
        // 首先检查是否有直接匹配的键（适用于扁平结构）
        $keys = explode('.', $path);
        $lastKey = end($keys);
        
        // 如果数据中直接包含了与最后一个键匹配的值，直接返回
        if (is_array($data) && array_key_exists($lastKey, $data)) {
            return $data[$lastKey];
        }
        
        // 尝试按照路径逐层查找（适用于嵌套结构）
        $value = $data;
        
        foreach ($keys as $key) {
            if (is_array($value) && array_key_exists($key, $value)) {
                $value = $value[$key];
            } elseif (is_object($value) && property_exists($value, $key)) {
                $value = $value->$key;
            } else {
                return null;
            }
        }
        
        // 处理日期时间对象
        if ($value instanceof \DateTime) {
            return $value->format('Y-m-d H:i:s');
        }
        
        return $value;
    }
    
    /**
     * 验证必填变量
     *
     * @param array $data 业务数据
     * @param array $template 模板数据
     * @return array 缺失的变量列表
     */
    protected function validateRequiredVariables(array $data, array $template): array
    {
        $config = json_decode($template['variables_config'] ?? '{}', true);
        $missingVars = [];
        
        if (isset($config['variables'])) {
            foreach ($config['variables'] as $var) {
                if (!empty($var['required']) && $this->getNestedValue($data, $var['field']) === null) {
                    $missingVars[] = $var['name'];
                }
            }
        }
        
        return $missingVars;
    }
} 