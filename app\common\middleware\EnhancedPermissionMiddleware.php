<?php
declare(strict_types=1);

namespace app\common\middleware;

use app\common\utils\PermissionAuditUtil;
use Closure;
use think\Request;
use think\Response;
use think\exception\HttpException;

/**
 * 增强权限验证中间件
 * 集成权限检查、审计日志、异常检测等功能
 */
class EnhancedPermissionMiddleware
{
    /**
     * 处理请求
     *
     * @param Request $request 请求对象
     * @param Closure $next 下一个中间件
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        
        try {
            // 1. 基础权限验证
            $this->validateBasicPermission($request);
            
            // 2. 执行请求
            $response = $next($request);
            
            // 3. 记录访问审计
            $this->logAccessAudit($request, $response, $startTime);
            
            // 4. 异常访问检测
            $this->detectAnomalousAccess($request);
            
            return $response;
            
        } catch (\Exception $e) {
            // 记录安全事件
            $this->logSecurityEvent($request, $e);
            throw $e;
        }
    }
    
    /**
     * 验证基础权限
     *
     * @param Request $request 请求对象
     * @throws HttpException
     */
    private function validateBasicPermission(Request $request): void
    {
        // 获取用户信息
        $adminInfo = $request->adminInfo ?? [];
        $adminId = $adminInfo['admin_id'] ?? 0;
        $adminData = $adminInfo['data'] ?? [];
        $tenantId = $adminData['tenant_id'] ?? 0;
        
        // 检查用户是否登录
        if (empty($adminId)) {
            PermissionAuditUtil::logSecurityEvent(
                PermissionAuditUtil::SECURITY_LEVEL_WARNING,
                '未授权访问尝试',
                [
                    'details' => [
                        'url' => $request->url(),
                        'method' => $request->method(),
                        'reason' => 'no_admin_id'
                    ]
                ]
            );
            throw new HttpException(401, '用户未登录或登录已过期');
        }
        
        // 超级管理员跳过权限检查
        if (is_super_admin()) {
            return;
        }
        
        // 检查租户状态
        if ($tenantId <= 0) {
            PermissionAuditUtil::logSecurityEvent(
                PermissionAuditUtil::SECURITY_LEVEL_DANGER,
                '无效租户访问',
                [
                    'admin_id' => $adminId,
                    'details' => [
                        'url' => $request->url(),
                        'tenant_id' => $tenantId
                    ]
                ]
            );
            throw new HttpException(403, '租户信息无效');
        }
        
        // 验证IP白名单（如果配置了）
        $this->validateIpWhitelist($request, $adminId, $tenantId);
        
        // 验证访问时间限制（如果配置了）
        $this->validateAccessTimeLimit($request, $adminId, $tenantId);
    }
    
    /**
     * 验证IP白名单
     *
     * @param Request $request 请求对象
     * @param int $adminId 管理员ID
     * @param int $tenantId 租户ID
     * @throws HttpException
     */
    private function validateIpWhitelist(Request $request, int $adminId, int $tenantId): void
    {
        $ipWhitelistEnabled = config('permission.security.ip_whitelist_enabled', false);
        
        if (!$ipWhitelistEnabled) {
            return;
        }
        
        $clientIp = $request->ip();
        $allowedIps = config('permission.security.allowed_ips', []);
        
        // 检查全局IP白名单
        if (!empty($allowedIps) && !in_array($clientIp, $allowedIps)) {
            // 检查用户特定的IP白名单
            $userAllowedIps = $this->getUserAllowedIps($adminId, $tenantId);
            
            if (empty($userAllowedIps) || !in_array($clientIp, $userAllowedIps)) {
                PermissionAuditUtil::logSecurityEvent(
                    PermissionAuditUtil::SECURITY_LEVEL_DANGER,
                    'IP白名单验证失败',
                    [
                        'admin_id' => $adminId,
                        'tenant_id' => $tenantId,
                        'details' => [
                            'client_ip' => $clientIp,
                            'allowed_ips' => $allowedIps,
                            'user_allowed_ips' => $userAllowedIps
                        ]
                    ]
                );
                throw new HttpException(403, 'IP地址不在允许范围内');
            }
        }
    }
    
    /**
     * 验证访问时间限制
     *
     * @param Request $request 请求对象
     * @param int $adminId 管理员ID
     * @param int $tenantId 租户ID
     * @throws HttpException
     */
    private function validateAccessTimeLimit(Request $request, int $adminId, int $tenantId): void
    {
        $timeLimitEnabled = config('permission.security.time_limit_enabled', false);
        
        if (!$timeLimitEnabled) {
            return;
        }
        
        $currentHour = (int)date('H');
        $allowedStartHour = config('permission.security.allowed_start_hour', 0);
        $allowedEndHour = config('permission.security.allowed_end_hour', 23);
        
        if ($currentHour < $allowedStartHour || $currentHour > $allowedEndHour) {
            PermissionAuditUtil::logSecurityEvent(
                PermissionAuditUtil::SECURITY_LEVEL_WARNING,
                '非允许时间访问',
                [
                    'admin_id' => $adminId,
                    'tenant_id' => $tenantId,
                    'details' => [
                        'current_hour' => $currentHour,
                        'allowed_start_hour' => $allowedStartHour,
                        'allowed_end_hour' => $allowedEndHour
                    ]
                ]
            );
            throw new HttpException(403, '当前时间不允许访问系统');
        }
    }
    
    /**
     * 记录访问审计
     *
     * @param Request $request 请求对象
     * @param Response $response 响应对象
     * @param float $startTime 开始时间
     */
    private function logAccessAudit(Request $request, Response $response, float $startTime): void
    {
        $adminInfo = $request->adminInfo ?? [];
        $adminId = $adminInfo['admin_id'] ?? 0;
        $adminData = $adminInfo['data'] ?? [];
        $tenantId = $adminData['tenant_id'] ?? 0;
        
        $executionTime = round((microtime(true) - $startTime) * 1000, 2); // 毫秒
        
        PermissionAuditUtil::logDataAccess([
            'admin_id' => $adminId,
            'tenant_id' => $tenantId,
            'resource' => $request->controller() . '/' . $request->action(),
            'action' => $request->method(),
            'query_conditions' => $request->param(),
            'response_code' => $response->getCode(),
            'execution_time' => $executionTime
        ]);
    }
    
    /**
     * 异常访问检测
     *
     * @param Request $request 请求对象
     */
    private function detectAnomalousAccess(Request $request): void
    {
        $adminInfo = $request->adminInfo ?? [];
        $adminId = $adminInfo['admin_id'] ?? 0;
        $adminData = $adminInfo['data'] ?? [];
        $tenantId = $adminData['tenant_id'] ?? 0;
        
        PermissionAuditUtil::detectAnomalousAccess([
            'admin_id' => $adminId,
            'tenant_id' => $tenantId,
            'url' => $request->url(),
            'method' => $request->method(),
            'params' => $request->param(),
            'query_conditions' => $request->param()
        ]);
    }
    
    /**
     * 记录安全事件
     *
     * @param Request $request 请求对象
     * @param \Exception $exception 异常对象
     */
    private function logSecurityEvent(Request $request, \Exception $exception): void
    {
        $adminInfo = $request->adminInfo ?? [];
        $adminId = $adminInfo['admin_id'] ?? 0;
        $adminData = $adminInfo['data'] ?? [];
        $tenantId = $adminData['tenant_id'] ?? 0;
        
        $level = match($exception->getCode()) {
            401 => PermissionAuditUtil::SECURITY_LEVEL_WARNING,
            403 => PermissionAuditUtil::SECURITY_LEVEL_DANGER,
            default => PermissionAuditUtil::SECURITY_LEVEL_INFO
        };
        
        PermissionAuditUtil::logSecurityEvent(
            $level,
            '权限验证异常',
            [
                'admin_id' => $adminId,
                'tenant_id' => $tenantId,
                'details' => [
                    'exception_class' => get_class($exception),
                    'exception_message' => $exception->getMessage(),
                    'exception_code' => $exception->getCode(),
                    'url' => $request->url(),
                    'method' => $request->method(),
                    'params' => $request->param()
                ]
            ]
        );
    }
    
    /**
     * 获取用户允许的IP列表
     *
     * @param int $adminId 管理员ID
     * @param int $tenantId 租户ID
     * @return array
     */
    private function getUserAllowedIps(int $adminId, int $tenantId): array
    {
        try {
            // 这里可以从数据库或缓存中获取用户特定的IP白名单
            // 示例实现
            return \think\facade\Db::table('system_admin_ip_whitelist')
                ->where('admin_id', $adminId)
                ->where('tenant_id', $tenantId)
                ->where('status', 1)
                ->column('ip_address');
                
        } catch (\Exception $e) {
            \think\facade\Log::error('获取用户IP白名单失败: ' . $e->getMessage(), [
                'admin_id' => $adminId,
                'tenant_id' => $tenantId
            ]);
            return [];
        }
    }
}
