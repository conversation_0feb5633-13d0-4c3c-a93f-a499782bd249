<?php
declare(strict_types=1);

namespace app\system\controller\tenant;

use app\common\core\base\BaseController;
use app\system\service\TenantConfigService;
use think\response\Json;

/**
 * 租户配置表控制器
 */
class TenantConfig extends BaseController
{
	
	/**
	 * 服务实例
	 *
	 * @var TenantConfigService
	 */
	protected TenantConfigService $service;
	
	/**
	 * 初始化
	 */
	protected function initialize(): void
	{
		parent::initialize();
		$this->service = TenantConfigService::getInstance();
	}
	
	/**
	 * 根据group获取配置
	 */
	public function detail(): Json
	{
		// todo sms配置，oss配置等等
		$configArr = [];
		$data      = [];
		foreach ($configArr as $item) {
			$data[$item] = $this->service->getInfo($item);
		}
		return $this->success('获取成功', $data);
	}
	
	/**
	 * 创建
	 */
	public function save(): J<PERSON>
	{
		return $this->service->create(input('group/s', ''), input('config/a'))
			? $this->success('创建成功')
			: $this->error('创建失败');
	}
	
} 